# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.24"
  }
  digests {
    sha256: "\205\213\220&\226\332\234\365\205\253\235\230\377\301\302q\"i\202\203T\337\351\020~7\021\260\204\243dh"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.0"
  }
  digests {
    sha256: "\267\227\232z\254\224\005_\r\237\037\323\264|\345\377\341\313`2\250B\272\237\276q\206\360\205(\221x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.24"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "\354\270\355\226\275k@O\242^E\003\305\316\336\302,-\354\347\343\275\340\355q:\317\a\\\035-\317"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "\035\341\304\220k\352>\020\026\214\254\027X\372\025\t\237X\021nL\215e\306^\276\"\350\003/\313D"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "|\212\330\322;\270\357\203\302\366z\227!:\276\000\216\035\347\255\225\212\027\224\005nVu\036-7\251"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "iris-rtc"
    version: "4.5.2-build.1"
  }
  digests {
    sha256: "\375W\0348\005\256sx{\301 ,\033\364\370\372}6\222E\343{\252\251\205k\036[\023\374\347\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-sdk"
    version: "4.5.2"
  }
  digests {
    sha256: "#\334\213l0\342\022\263\232o\207\331\330%y\'\241)\366\255\242\021\033E\262\242\212\025\301\24015"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-rtc-basic"
    version: "4.5.2"
  }
  digests {
    sha256: "\"\335\235\250\306_g\361\"\3015\n\325\237\270!\320<P:\245T[)\247[\266\177\215\204\021\311"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.infra"
    artifactId: "aosl"
    version: "1.2.13.1"
  }
  digests {
    sha256: "\017\307\305\365\275\372\267\316\214m\315\006m\324\354\305\313\353h\315\r\237?\250>\001\205q\020Ee\204"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "ains"
    version: "4.5.2"
  }
  digests {
    sha256: "\361\320\351P\001\017\262,\364\251~\267\351\306=\305?\220f\270 \373\r\277\251*\260\317~\004\a\263"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "ains-ll"
    version: "4.5.2"
  }
  digests {
    sha256: "\304\254:S\232\244\211\231\332\264\235\206\322)\320\304\001\374T\306_\372\037\224\032\362j\257\005j\233["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "audio-beauty"
    version: "4.5.2"
  }
  digests {
    sha256: "\026Z\026R$\247y\2200\317|\365\257&76\f\276)\nX\005[\336\313@q\tI\237/L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "clear-vision"
    version: "4.5.2"
  }
  digests {
    sha256: "\322%)k\313Af\325:\362\222\200t\375\203\242\363\265h,\232\275\227+%\275F(~D\252z"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-content-inspect"
    version: "4.5.2"
  }
  digests {
    sha256: "C\247\337UWjM\302\021q\276\352\346\027\300\214\222\022\204\025\275@\233@\204\234 R\276@q\311"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "screen-capture"
    version: "4.5.2"
  }
  digests {
    sha256: "\250\345\026\335\363\235Wr\346\203f\302\t\344\236\257E\253\246\030p\004\303\032\367\315wb\370\341\364\274"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-virtual-background"
    version: "4.5.2"
  }
  digests {
    sha256: "\005\177\234\306v\025\017\272d\346T$\303MJ<\235\255XN\222\353u\266Y-\251\">\304\354f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "spatial-audio"
    version: "4.5.2"
  }
  digests {
    sha256: "\030\025B\361\207\216\245\211\026\204\273{\362C\253\3037\325\332!c\027\364\025\200\254\263\334!\203!\373"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-screen-sharing"
    version: "4.5.2"
  }
  digests {
    sha256: "pHLN\210\370\266!\224\2155\354\003\355\223AL\345)\257\241\377\265`\a\001\325&\202\203\311>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "aiaec"
    version: "4.5.2"
  }
  digests {
    sha256: "\243\271\004\252\343G\323\227\022\0161 \3675\271\247-\304=\271UM\022\220\330\245\031\2705\265\336D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "aiaec-ll"
    version: "4.5.2"
  }
  digests {
    sha256: "\354\023\006Z\250\273\270\276K\353\235*\031\327\210z\335\3377m\250\344\000\277es+_\374\334\341\304"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-vqa"
    version: "4.5.2"
  }
  digests {
    sha256: "~-\224\255\223YH\022e\223<&\021\317\202\272\316\027\231fc&\373\004,\374\311^X*[\204"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-face-detect"
    version: "4.5.2"
  }
  digests {
    sha256: "\036\340Z\3662xw\354\023\242\215\270\320\261.0\326x\207\301\351\306\317\271\b\352O\a\364\3326\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-face-capture"
    version: "4.5.2"
  }
  digests {
    sha256: "\345\250\331ru\003\206 z;z\215\366N\031\003\303\327\215\276(:^\356\343\n\035\260\340\016\300r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-voice-drive"
    version: "4.5.2"
  }
  digests {
    sha256: "\3670&\251L\237\313lW\t\271\303\340>\261\360\217\324\362\341\n\274\"\027\372\377*_P|\2569"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-video-codec-enc"
    version: "4.5.2"
  }
  digests {
    sha256: "\371J\220\353\377\3773\250\030\020QX\033\t\372]\v\312\006\351p\234\204\245^\262\261\016\275k\353\031"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-video-codec-dec"
    version: "4.5.2"
  }
  digests {
    sha256: "\006\177\242k\220\372O\264o\276k\022,b\200\250@\227\351=\353\274\277\366{E\305\335\n\264+m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-video-av1-codec-enc"
    version: "4.5.2"
  }
  digests {
    sha256: "\206\002\211\320\240\332\332i?.\022\025\365\233L\213\327.\002.\207e\3359nc\223\024 \205\306\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.agora.rtc"
    artifactId: "full-video-av1-codec-dec"
    version: "4.5.2"
  }
  digests {
    sha256: "\353.\3741p\037\317\\\241i\020:K\2465\024?\342s\3170Sy\345\206J\301\005\307O`\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "\367\226\251G$\364Y\020\031X\265\311\326q\274\241\266$k\315V\033I\365\177\343\211\023\234\240\307["
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.3"
  }
  digests {
    sha256: "~E\322\002=\301}q_\377\363\334W\222\367\230d,J\341\034\224\200\006\261u*\271\355\332W\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.9.3"
  }
  digests {
    sha256: "\342\3324\366\214\254Uy\206(\001\230\345\301Tk\212$Ri\260P\261\314\220\265i\026\333\366\222\001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.7.1"
  }
  digests {
    sha256: "\217\254\271\a\030\317R^\257\376m\033\326\024]\377\025mj\345\035<\037\202\002\315\005\216\370\201\366*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.2.0"
  }
  digests {
    sha256: "\343\336\373^\343\205wWP]z\326bu\334:\v\001Ai0\033\211\326\344\356\300;\016E\274\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.2.0"
  }
  digests {
    sha256: "\2504\302\027\331s\021\'wS=\257\022\201\332\263;\327x\372\335-f\276\2749\265\376\201\250\220\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window.extensions.core"
    artifactId: "core"
    version: "1.0.0"
  }
  digests {
    sha256: "1\000\205K-\300\336\\M\210\264~\034]\263\b\001w\f\202\035\312(\030\224C\267bO@\b\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "me.carda"
    artifactId: "AndroidAwnCore"
    version: "0.10.0"
  }
  digests {
    sha256: "\0009\331%m\037xyB\0049\312Zs\376\021F\205\331\036\341\301\235\037\260]D>\354\032\205J"
  }
  repo_index {
    value: 10
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "33.0.0-android"
  }
  digests {
    sha256: "d\0055\001J0LP\"\032\272\321\235k\"\240\226\3611\031\307\304G\246U\314\036\000\2322 \215"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.2"
  }
  digests {
    sha256: "\212\217\201\317\2335\236?m\372i\032\036wi\205\300a\357/\"<\233,\200u>\033E\216\200d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.41.0"
  }
  digests {
    sha256: "/\237$[\366\216BY\326\020\211O$\006\334\037cc\334c\223\002\275Vn\202r\344\364T\021r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "me.leolin"
    artifactId: "ShortcutBadger"
    version: "1.1.22"
  }
  digests {
    sha256: "\315\026\020\334H\305i\222)P!\207\375\303\265\305C8p\325\252\261Y3!!\261\370\301\332\330\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.1.0"
  }
  digests {
    sha256: "\242+\224\247w\211\363\263K\354\322@\202#\023\000R@\021\314\310?\002aB\304\232\326\00117\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.11.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.0"
  }
  digests {
    sha256: "L\023\360\373\000\303z\373f\344\313\307]h\250\n\322\327=g\247\350[\r\244\225\200\250!\273\f9"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.0.0"
  }
  digests {
    sha256: "\002\271\240\234N\f\261z\r\211<\277@\3741\323p\2262a\233\v\375%\036\\Q\343\347\256\327\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.3.0"
  }
  digests {
    sha256: "6\253\374C\227P\270%\355\333?W\a\347\357\002\034K8\354\341\334r\202f\205\256\273\327\000L3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.4"
  }
  digests {
    sha256: "6@\b$\001O\306\035\223y\261=\321\253\023\366QHG\373\0044|\017\337\346\030\272\316x\363\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.6.1"
  }
  digests {
    sha256: "]\r\\\350i\332c\004\2754\342\027\273\367e`\034Q\306\036\305\305\177/!\233\232\210\024\321\370\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-crashlytics"
    version: "19.4.2"
  }
  digests {
    sha256: "\003I\322\217\311\370\274<\370\235YtH\263w\000m#\216 x\314]\375\302JI\344\234\253Ae"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-sessions"
    version: "2.1.0"
  }
  digests {
    sha256: "\304\023\260\241\321X\270\221Sb\313\240\210\231)P\252K\207\365\001\313\215\360t?\002\233($e&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.2.0"
  }
  digests {
    sha256: "\335\033b\b\243h\224\347\327\357\376\"\003\262Du\256\265\fM0E\243\030\020\256:\325\276C\273Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.1"
  }
  digests {
    sha256: "Jt+\237>\366\201\315\3256~\n\a\266\346v\240\203I\306\346\342\024\301\205\024\230\207\223\020\262\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "19.0.0"
  }
  digests {
    sha256: "\005\0035\n\343\021\363\202T:\2108C\325\005\v\243\2355\363?\222\263\311\327\346\026R\210\260\273\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.2.0"
  }
  digests {
    sha256: "\342U-\035\307\\r\200\b\234\t\376\274\257/\356r\207\305\2368\305G\315JC\036\335_M4\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.3.0"
  }
  digests {
    sha256: "*j\235\211\362J\267\337\t3\230:\214Hl!o\263\'G\276\247+1\23162:q\226A\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.3.0"
  }
  digests {
    sha256: "\025\371\301O\300z\214\222@+.\006V\350\351B\222\262E\350\3269],\tm\020\222\r\223\313\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-config-interop"
    version: "16.0.1"
  }
  digests {
    sha256: "m\273\f\361\304nE\375\360;\376g+\247H3`\273\247\000\360\017\016dq\177p\017y\212\177\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "20.0.1"
  }
  digests {
    sha256: "\267k\343\215\245O\227+\303v\3776YIN\353\307\0303\236\3443MBk\264\365.Y\365\352\344"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-dynamic-links"
    version: "22.1.0"
  }
  digests {
    sha256: "\234<:\327\331\216\034\265\343\365\301\247z\031\b\215E\261-\207,-z\374mF\343\241\2505gV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "25.1.3"
  }
  digests {
    sha256: "\374\376\'\304t~\311\326\021\261h\216\027\366\264\317,\277\263\035\327v\341\260\233\037\252\347\246;\377\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.1"
  }
  digests {
    sha256: "\355\327\206\202D\343\366x\226\357\260\304\306\274\233N\212\262W\v0\217E\312\376G\352\210x\225\346q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.25.5"
  }
  digests {
    sha256: "y\243\377Q\254*\213\033\347\377\372\"\204\203\326It\361\177>\377\377\331f\260T\330qy\344V4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.62.2"
  }
  digests {
    sha256: "%D\222\016\245\364g \334\367^\202\000\243t\320\323v\375\310\362\224({\265yu\272\263\202\2469"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.62.2"
  }
  digests {
    sha256: ".\211iD\317Q>\016\\\3752\274\327,\211`\032\'\306\312V\221o\204\262\017:\023\272\317\033\037"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.62.2"
  }
  digests {
    sha256: "\030C\231\002\304s\242\301Q\036Q}\023\270\256ycx\205\n\216\332Cx|k\247x\372\220\374\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.23"
  }
  digests {
    sha256: "\237\376Rk\364:cH\351\330\263;\234\326\365\200\247\365\356\320\317\005Y\023\000~\332&=\351t\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.26.0"
  }
  digests {
    sha256: "\267\322>\223\243E7\3163\'\b&\232\r\024\004x\212[^\031I\350/U5\374\345\033>\251["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.62.2"
  }
  digests {
    sha256: "\231Yt}\366\247S\021\236\034\032=\377\001\252vm$U\365\344\206\n\312\243\0055\236\035S:\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.62.2"
  }
  digests {
    sha256: "\236\220?\375+0\322\373{T\312Mr\221[\032\263[\r\"\274\220\230V[\016\217B(\325\232\376"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-util"
    version: "1.62.2"
  }
  digests {
    sha256: "<q\003\346\363s\205q\343\256\332B\017\342\246\254h\343TSM\213f\364\030\227\266u[H\2675"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.62.2"
  }
  digests {
    sha256: "y\231y\211\250\302\265\277M\321\201\202\242\337./f\207\003\326\213\247\303\027\347\240x\t\323?\221\364"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.62.2"
  }
  digests {
    sha256: "\373L\246y\244!AC@le\254Ag\262\265\342\356,\253\037\301\001Vk\261\304i]\020^6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.1"
  }
  digests {
    sha256: "K\301\327\263\205\307\r\374\006\330c\302\321\020|<\206cS\247\030\020\364\036D\227\250\355\335\231\027\216"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "21.0.1"
  }
  digests {
    sha256: "\365\f\3174\217\366\216\241h\346\327\241\372\356g5\370\322q\3461q\333g\350\270\337\315t\342=w"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "18.0.0"
  }
  digests {
    sha256: "\322z\363\363\034\214\316\241\351\003\f\035\303\204\021\304\v\230g?\027~\3763\344\361\265\021\360\366Y\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.android"
    artifactId: "facebook-login"
    version: "18.0.3"
  }
  digests {
    sha256: "0\240\273y\267BD\"\214c\020\034&5:\326\032\227>\025l\245[q+Y\376V\374\225!\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.android"
    artifactId: "facebook-core"
    version: "18.0.3"
  }
  digests {
    sha256: "\303-\206i\354]\324y\267\274>6Y\306\274\352\273\326\177\360\377\246p\002W\177\025\274\361\325Dk"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.android.installreferrer"
    artifactId: "installreferrer"
    version: "1.0"
  }
  digests {
    sha256: "o\200\357\221$4\250D%\227z\220\264\032\262bY\367\320\311S\355\257\220\003\236}\001\324C\203\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.android"
    artifactId: "facebook-bolts"
    version: "18.0.3"
  }
  digests {
    sha256: "\005\306\272\340\023\004\207\346:\335\005\205J;\275!F\\\006\242\225\242\020K9H\330u\2256\266\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.android"
    artifactId: "facebook-common"
    version: "18.0.3"
  }
  digests {
    sha256: "b\255\373\034\320Q\204\006]i\003:\237\221w\t\263\222\000O\207\250\252\034\301\304\360\351\373\\\247\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.zxing"
    artifactId: "core"
    version: "3.5.2"
  }
  digests {
    sha256: "\373\251\001\"\265\325k\254\214\224|`\265\222R\021\260\330\324\fS\2331\t\2767t\360\217t\376\333"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.7"
  }
  digests {
    sha256: "\016\217\0302&l[\006g\255=;\020\230\346$\344\232\t\aT\223\240\024\247\350\212\360\037\323\n\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.heifwriter"
    artifactId: "heifwriter"
    version: "1.0.0"
  }
  digests {
    sha256: "\'\017\230}\005\230\314\0221\252\236\243\177Q^KR\317\345\304<\036\240\036\277\033\313@*Z8T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.6"
  }
  digests {
    sha256: "\370w\323\004f\n\302\241B\363\206[\255\374\227\035\354~\327<t|\177\215]/Q9\312se\023"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.12.1"
  }
  digests {
    sha256: "\030\240\245\032\321)\340\310V?\003\035F\310\247\346\021i\365j\350NV*\ruhiA&!\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.security"
    artifactId: "security-crypto"
    version: "1.1.0-alpha06"
  }
  digests {
    sha256: "\227a\021\027\v?\023\322\360\371E}\237\237W\223G\024<\266\\\005Be\220m3\307a\212L*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.crypto.tink"
    artifactId: "tink-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\324\233\335\232\302I\340\232q}Q\337\243jT\377%\360\237\371\016\017\2740\237S\207\236i\313x\211"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "21.3.0"
  }
  digests {
    sha256: "\325\002C\204\325\277f\307^\3375=\225\353\216{[\366D\203\023\204t\306\342\351>,#\224\370B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-maps"
    version: "18.2.0"
  }
  digests {
    sha256: "D\365\006UW\212!\305y\341\273-\254\256\304\305E\354M\370\023\223\326\375r6\326d&\200V\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.maps.android"
    artifactId: "android-maps-utils"
    version: "3.6.0"
  }
  digests {
    sha256: "\240\333\357\r\027\225\340Rwf\236\246u\352\212WI\341_\310\356{\3016\v\3461+\351`\211\252"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "review"
    version: "2.0.2"
  }
  digests {
    sha256: "\026\002\002fx#\035\272\017t\202\321$NE\377\203\331\377\"\374\213mk\350\226\022(\254z2\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.4.1"
  }
  digests {
    sha256: "f\315\017\231 \221\221f\b#\323,\217\303)\343\203\250\247\3773\340\326vc#\2076\032\274\272p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.4.1"
  }
  digests {
    sha256: "\227>^{\016\317\216l\214\330%\312\263QE\270LV\002\n\367\303\354R\201^Y^\367\033\252\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.4.1"
  }
  digests {
    sha256: "e\266\322,\226\337\265\316vuK\b\365\260\004\356\r\275\276`y<^\253\237\225\301\365O\225\364\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.4.1"
  }
  digests {
    sha256: "!p\256dH\315I\237\305p\332\372\201\345hz\330\023\231[|+\177\366\200\032\312\252\232\210\273J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.4.1"
  }
  digests {
    sha256: "\034\v\264\036\350\212k\372\362G \273\376-)\2403\253\255!\303\016\t!\357\275\316l\037\242\210B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.4.1"
  }
  digests {
    sha256: "\367\331|Z9\334\243\303\302\033\310\247\001E\3311\330c>\320\332\350\221\230\303p\347\267\343\303\361\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-dash"
    version: "1.4.1"
  }
  digests {
    sha256: "9@\306i5\327\030\320z\272\256\005\261\032\270\227Q\263\000\267\316\344\270\255\241\272\225\020\3476\'Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-hls"
    version: "1.4.1"
  }
  digests {
    sha256: "CTkk.\360\302\201o]:\361\201\302\027\232\\\033\335\271\337\205\267\270K4(\3157\310\207\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-rtsp"
    version: "1.4.1"
  }
  digests {
    sha256: "\206:j\004\327\027.S\020\217}\307\035\23216v\260E\\\372\333S\270\314$a7:\326\316x"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-smoothstreaming"
    version: "1.4.1"
  }
  digests {
    sha256: "\016q@\353\221\a\232bI\372\301p#\aS\312e\224\347j!\375\236\202\345\364\254\361\230\000\347N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.4.1"
  }
  digests {
    sha256: "\037Q\344c>\036a7\360\244\270\005R\257\303\231\342i \213p\003R\371eC\324Q;\346\277("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.journeyapps"
    artifactId: "zxing-android-embedded"
    version: "4.3.0"
  }
  digests {
    sha256: "J\2603S\022|4\345\\\273\200\373\311\243@1\336\313\317\261\354^\217\231\031D\322\324\224b\0323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "\005\360\377\036\372\206\261\253\017\335\017\337\352Fid[cF\257*\376\030\201\222\351\350r\242\244*\362"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 3
  library_dep_index: 1
}
library_dependencies {
  library_index: 4
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 70
  library_dep_index: 33
  library_dep_index: 56
  library_dep_index: 45
  library_dep_index: 77
  library_dep_index: 80
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 61
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 0
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
  library_dep_index: 38
  library_dep_index: 0
}
library_dependencies {
  library_index: 38
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 36
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 0
}
library_dependencies {
  library_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 70
}
library_dependencies {
  library_index: 42
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 44
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 42
  library_dep_index: 42
}
library_dependencies {
  library_index: 45
  library_dep_index: 33
  library_dep_index: 46
  library_dep_index: 42
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 56
  library_dep_index: 68
  library_dep_index: 1
  library_dep_index: 69
  library_dep_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
}
library_dependencies {
  library_index: 47
  library_dep_index: 33
  library_dep_index: 48
}
library_dependencies {
  library_index: 49
  library_dep_index: 1
  library_dep_index: 1
}
library_dependencies {
  library_index: 50
  library_dep_index: 33
}
library_dependencies {
  library_index: 51
  library_dep_index: 33
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 32
  library_dep_index: 54
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 61
}
library_dependencies {
  library_index: 52
  library_dep_index: 33
}
library_dependencies {
  library_index: 53
  library_dep_index: 33
  library_dep_index: 52
}
library_dependencies {
  library_index: 54
  library_dep_index: 33
  library_dep_index: 47
  library_dep_index: 55
  library_dep_index: 48
}
library_dependencies {
  library_index: 55
  library_dep_index: 33
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 33
}
library_dependencies {
  library_index: 57
  library_dep_index: 33
  library_dep_index: 32
  library_dep_index: 32
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 61
}
library_dependencies {
  library_index: 58
  library_dep_index: 33
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 61
}
library_dependencies {
  library_index: 59
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 61
}
library_dependencies {
  library_index: 60
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 32
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 62
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 61
  library_dep_index: 60
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 62
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 62
  library_dep_index: 33
  library_dep_index: 51
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 63
  library_dep_index: 59
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 62
  library_dep_index: 59
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 33
  library_dep_index: 65
  library_dep_index: 60
  library_dep_index: 59
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 32
  library_dep_index: 57
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 51
  library_dep_index: 62
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 61
}
library_dependencies {
  library_index: 65
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 1
  library_dep_index: 45
  library_dep_index: 1
}
library_dependencies {
  library_index: 66
  library_dep_index: 33
  library_dep_index: 52
  library_dep_index: 32
  library_dep_index: 1
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 66
}
library_dependencies {
  library_index: 68
  library_dep_index: 33
  library_dep_index: 42
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
  library_dep_index: 33
  library_dep_index: 46
  library_dep_index: 42
  library_dep_index: 65
  library_dep_index: 60
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 73
  library_dep_index: 54
  library_dep_index: 66
  library_dep_index: 74
  library_dep_index: 1
  library_dep_index: 76
}
library_dependencies {
  library_index: 71
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 64
  library_dep_index: 54
  library_dep_index: 66
  library_dep_index: 56
  library_dep_index: 1
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 71
  library_dep_index: 65
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 67
  library_dep_index: 1
  library_dep_index: 71
}
library_dependencies {
  library_index: 73
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 60
  library_dep_index: 59
  library_dep_index: 42
}
library_dependencies {
  library_index: 74
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 42
}
library_dependencies {
  library_index: 76
  library_dep_index: 72
  library_dep_index: 44
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 61
  library_dep_index: 63
  library_dep_index: 67
  library_dep_index: 1
  library_dep_index: 70
}
library_dependencies {
  library_index: 77
  library_dep_index: 45
  library_dep_index: 78
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 79
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 77
}
library_dependencies {
  library_index: 79
  library_dep_index: 33
  library_dep_index: 1
}
library_dependencies {
  library_index: 81
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 45
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
  library_dep_index: 88
  library_dep_index: 33
  library_dep_index: 89
  library_dep_index: 98
  library_dep_index: 81
  library_dep_index: 107
  library_dep_index: 112
  library_dep_index: 114
  library_dep_index: 58
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
  library_dep_index: 48
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 89
  library_dep_index: 71
  library_dep_index: 33
  library_dep_index: 90
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 65
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 70
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 54
  library_dep_index: 97
  library_dep_index: 66
  library_dep_index: 1
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 89
}
library_dependencies {
  library_index: 91
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 42
}
library_dependencies {
  library_index: 92
  library_dep_index: 91
  library_dep_index: 50
  library_dep_index: 42
}
library_dependencies {
  library_index: 93
  library_dep_index: 33
}
library_dependencies {
  library_index: 94
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 75
}
library_dependencies {
  library_index: 95
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 58
  library_dep_index: 55
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 95
  library_dep_index: 95
}
library_dependencies {
  library_index: 97
  library_dep_index: 33
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
  library_dep_index: 87
  library_dep_index: 71
  library_dep_index: 33
  library_dep_index: 89
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 45
  library_dep_index: 94
  library_dep_index: 104
  library_dep_index: 46
  library_dep_index: 70
  library_dep_index: 51
  library_dep_index: 109
  library_dep_index: 97
  library_dep_index: 110
  library_dep_index: 91
  library_dep_index: 111
}
library_dependencies {
  library_index: 99
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 100
  library_dep_index: 33
}
library_dependencies {
  library_index: 101
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 75
  library_dep_index: 42
}
library_dependencies {
  library_index: 102
  library_dep_index: 89
  library_dep_index: 45
  library_dep_index: 103
}
library_dependencies {
  library_index: 104
  library_dep_index: 45
  library_dep_index: 42
  library_dep_index: 105
}
library_dependencies {
  library_index: 105
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 106
  library_dep_index: 73
  library_dep_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 106
  library_dep_index: 33
}
library_dependencies {
  library_index: 107
  library_dep_index: 33
}
library_dependencies {
  library_index: 108
  library_dep_index: 33
}
library_dependencies {
  library_index: 109
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 75
  library_dep_index: 42
}
library_dependencies {
  library_index: 110
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 104
}
library_dependencies {
  library_index: 111
  library_dep_index: 33
  library_dep_index: 70
  library_dep_index: 109
  library_dep_index: 45
  library_dep_index: 42
}
library_dependencies {
  library_index: 112
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 113
}
library_dependencies {
  library_index: 113
  library_dep_index: 33
  library_dep_index: 112
  library_dep_index: 1
  library_dep_index: 112
}
library_dependencies {
  library_index: 114
  library_dep_index: 46
  library_dep_index: 53
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 115
}
library_dependencies {
  library_index: 115
  library_dep_index: 33
  library_dep_index: 0
  library_dep_index: 114
}
library_dependencies {
  library_index: 116
  library_dep_index: 117
  library_dep_index: 134
  library_dep_index: 137
  library_dep_index: 166
  library_dep_index: 167
  library_dep_index: 183
  library_dep_index: 187
  library_dep_index: 136
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 188
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
  library_dep_index: 42
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 70
  library_dep_index: 107
  library_dep_index: 122
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 127
  library_dep_index: 129
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 1
}
library_dependencies {
  library_index: 118
  library_dep_index: 33
  library_dep_index: 46
  library_dep_index: 42
  library_dep_index: 47
  library_dep_index: 45
  library_dep_index: 50
  library_dep_index: 48
}
library_dependencies {
  library_index: 119
  library_dep_index: 33
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 120
}
library_dependencies {
  library_index: 120
  library_dep_index: 119
  library_dep_index: 121
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 1
  library_dep_index: 119
}
library_dependencies {
  library_index: 121
  library_dep_index: 70
  library_dep_index: 73
  library_dep_index: 122
  library_dep_index: 124
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 125
  library_dep_index: 40
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 123
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 70
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 124
  library_dep_index: 42
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 125
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 126
  library_dep_index: 1
  library_dep_index: 0
}
library_dependencies {
  library_index: 127
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 128
}
library_dependencies {
  library_index: 129
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 127
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 39
}
library_dependencies {
  library_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 132
  library_dep_index: 123
  library_dep_index: 40
}
library_dependencies {
  library_index: 133
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 130
  library_dep_index: 134
}
library_dependencies {
  library_index: 134
  library_dep_index: 39
  library_dep_index: 135
  library_dep_index: 130
  library_dep_index: 33
  library_dep_index: 47
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 135
  library_dep_index: 130
  library_dep_index: 33
  library_dep_index: 87
}
library_dependencies {
  library_index: 136
  library_dep_index: 134
  library_dep_index: 0
  library_dep_index: 135
  library_dep_index: 130
}
library_dependencies {
  library_index: 137
  library_dep_index: 138
  library_dep_index: 40
  library_dep_index: 130
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 164
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 142
  library_dep_index: 139
  library_dep_index: 165
  library_dep_index: 0
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 146
  library_dep_index: 33
}
library_dependencies {
  library_index: 138
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 139
  library_dep_index: 130
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 0
  library_dep_index: 142
  library_dep_index: 143
  library_dep_index: 144
  library_dep_index: 131
  library_dep_index: 33
  library_dep_index: 148
}
library_dependencies {
  library_index: 139
  library_dep_index: 40
  library_dep_index: 130
}
library_dependencies {
  library_index: 140
  library_dep_index: 33
}
library_dependencies {
  library_index: 141
  library_dep_index: 0
  library_dep_index: 33
  library_dep_index: 140
}
library_dependencies {
  library_index: 142
  library_dep_index: 40
  library_dep_index: 130
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 139
  library_dep_index: 1
}
library_dependencies {
  library_index: 143
  library_dep_index: 33
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 146
  library_dep_index: 0
}
library_dependencies {
  library_index: 144
  library_dep_index: 33
}
library_dependencies {
  library_index: 145
  library_dep_index: 144
  library_dep_index: 146
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 33
}
library_dependencies {
  library_index: 146
  library_dep_index: 144
  library_dep_index: 33
  library_dep_index: 131
  library_dep_index: 140
  library_dep_index: 147
}
library_dependencies {
  library_index: 147
  library_dep_index: 33
  library_dep_index: 140
}
library_dependencies {
  library_index: 148
  library_dep_index: 149
}
library_dependencies {
  library_index: 149
  library_dep_index: 150
  library_dep_index: 160
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 150
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 163
}
library_dependencies {
  library_index: 150
  library_dep_index: 151
}
library_dependencies {
  library_index: 151
  library_dep_index: 33
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 158
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 148
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 163
}
library_dependencies {
  library_index: 152
  library_dep_index: 153
}
library_dependencies {
  library_index: 153
  library_dep_index: 33
  library_dep_index: 154
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 150
  library_dep_index: 156
  library_dep_index: 148
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 163
}
library_dependencies {
  library_index: 154
  library_dep_index: 1
  library_dep_index: 155
}
library_dependencies {
  library_index: 155
  library_dep_index: 1
}
library_dependencies {
  library_index: 156
  library_dep_index: 157
}
library_dependencies {
  library_index: 157
  library_dep_index: 152
  library_dep_index: 158
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 150
  library_dep_index: 152
  library_dep_index: 148
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 163
}
library_dependencies {
  library_index: 158
  library_dep_index: 159
}
library_dependencies {
  library_index: 159
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 160
  library_dep_index: 161
}
library_dependencies {
  library_index: 161
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 162
  library_dep_index: 158
  library_dep_index: 1
  library_dep_index: 150
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 148
  library_dep_index: 162
  library_dep_index: 163
}
library_dependencies {
  library_index: 162
  library_dep_index: 163
  library_dep_index: 150
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 148
  library_dep_index: 160
  library_dep_index: 163
}
library_dependencies {
  library_index: 163
  library_dep_index: 150
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 148
  library_dep_index: 160
  library_dep_index: 162
}
library_dependencies {
  library_index: 164
  library_dep_index: 141
  library_dep_index: 140
}
library_dependencies {
  library_index: 165
  library_dep_index: 41
  library_dep_index: 130
}
library_dependencies {
  library_index: 166
  library_dep_index: 40
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 165
  library_dep_index: 33
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 1
}
library_dependencies {
  library_index: 167
  library_dep_index: 168
  library_dep_index: 40
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 170
  library_dep_index: 33
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 171
  library_dep_index: 179
  library_dep_index: 181
  library_dep_index: 182
  library_dep_index: 1
  library_dep_index: 36
}
library_dependencies {
  library_index: 168
  library_dep_index: 169
}
library_dependencies {
  library_index: 170
  library_dep_index: 123
}
library_dependencies {
  library_index: 171
  library_dep_index: 172
  library_dep_index: 173
  library_dep_index: 83
}
library_dependencies {
  library_index: 172
  library_dep_index: 85
  library_dep_index: 87
  library_dep_index: 83
}
library_dependencies {
  library_index: 173
  library_dep_index: 172
  library_dep_index: 174
  library_dep_index: 175
  library_dep_index: 176
  library_dep_index: 87
  library_dep_index: 83
  library_dep_index: 177
  library_dep_index: 178
}
library_dependencies {
  library_index: 178
  library_dep_index: 172
}
library_dependencies {
  library_index: 179
  library_dep_index: 172
  library_dep_index: 180
  library_dep_index: 173
  library_dep_index: 158
  library_dep_index: 83
  library_dep_index: 177
}
library_dependencies {
  library_index: 180
  library_dep_index: 172
  library_dep_index: 173
  library_dep_index: 176
  library_dep_index: 83
}
library_dependencies {
  library_index: 181
  library_dep_index: 172
  library_dep_index: 169
  library_dep_index: 85
  library_dep_index: 83
}
library_dependencies {
  library_index: 182
  library_dep_index: 172
  library_dep_index: 83
  library_dep_index: 87
}
library_dependencies {
  library_index: 183
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 143
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 147
  library_dep_index: 184
  library_dep_index: 142
  library_dep_index: 139
  library_dep_index: 165
  library_dep_index: 33
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 146
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 185
  library_dep_index: 186
  library_dep_index: 87
  library_dep_index: 1
}
library_dependencies {
  library_index: 184
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 185
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 186
  library_dep_index: 105
  library_dep_index: 41
}
library_dependencies {
  library_index: 187
  library_dep_index: 130
  library_dep_index: 188
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 135
  library_dep_index: 33
  library_dep_index: 123
  library_dep_index: 40
  library_dep_index: 1
  library_dep_index: 36
}
library_dependencies {
  library_index: 188
  library_dep_index: 40
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 33
  library_dep_index: 123
  library_dep_index: 1
}
library_dependencies {
  library_index: 189
  library_dep_index: 89
  library_dep_index: 1
  library_dep_index: 190
  library_dep_index: 193
}
library_dependencies {
  library_index: 190
  library_dep_index: 33
  library_dep_index: 105
  library_dep_index: 191
  library_dep_index: 65
  library_dep_index: 1
  library_dep_index: 192
}
library_dependencies {
  library_index: 192
  library_dep_index: 33
  library_dep_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 193
  library_dep_index: 89
  library_dep_index: 100
  library_dep_index: 118
  library_dep_index: 71
  library_dep_index: 70
  library_dep_index: 194
  library_dep_index: 1
  library_dep_index: 190
  library_dep_index: 195
}
library_dependencies {
  library_index: 195
  library_dep_index: 45
  library_dep_index: 81
  library_dep_index: 105
  library_dep_index: 196
  library_dep_index: 70
}
library_dependencies {
  library_index: 196
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 105
  library_dep_index: 75
  library_dep_index: 74
  library_dep_index: 101
  library_dep_index: 94
  library_dep_index: 197
  library_dep_index: 50
  library_dep_index: 198
  library_dep_index: 199
  library_dep_index: 93
}
library_dependencies {
  library_index: 197
  library_dep_index: 33
  library_dep_index: 75
  library_dep_index: 45
  library_dep_index: 78
  library_dep_index: 110
}
library_dependencies {
  library_index: 198
  library_dep_index: 33
  library_dep_index: 45
  library_dep_index: 50
}
library_dependencies {
  library_index: 199
  library_dep_index: 33
  library_dep_index: 45
}
library_dependencies {
  library_index: 200
  library_dep_index: 33
}
library_dependencies {
  library_index: 201
  library_dep_index: 33
}
library_dependencies {
  library_index: 203
  library_dep_index: 33
  library_dep_index: 46
  library_dep_index: 45
}
library_dependencies {
  library_index: 204
  library_dep_index: 33
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 205
}
library_dependencies {
  library_index: 205
  library_dep_index: 33
  library_dep_index: 85
  library_dep_index: 174
  library_dep_index: 87
}
library_dependencies {
  library_index: 206
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 1
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 207
  library_dep_index: 70
  library_dep_index: 123
  library_dep_index: 41
  library_dep_index: 40
}
library_dependencies {
  library_index: 209
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 128
}
library_dependencies {
  library_index: 210
  library_dep_index: 33
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 200
  library_dep_index: 211
  library_dep_index: 212
  library_dep_index: 214
  library_dep_index: 215
  library_dep_index: 220
  library_dep_index: 213
}
library_dependencies {
  library_index: 211
  library_dep_index: 33
  library_dep_index: 83
  library_dep_index: 46
  library_dep_index: 212
  library_dep_index: 213
  library_dep_index: 214
  library_dep_index: 215
  library_dep_index: 210
  library_dep_index: 216
  library_dep_index: 217
  library_dep_index: 218
  library_dep_index: 219
  library_dep_index: 220
}
library_dependencies {
  library_index: 212
  library_dep_index: 33
  library_dep_index: 211
}
library_dependencies {
  library_index: 213
  library_dep_index: 211
  library_dep_index: 33
}
library_dependencies {
  library_index: 214
  library_dep_index: 211
  library_dep_index: 213
  library_dep_index: 33
  library_dep_index: 200
}
library_dependencies {
  library_index: 215
  library_dep_index: 211
  library_dep_index: 33
}
library_dependencies {
  library_index: 216
  library_dep_index: 210
  library_dep_index: 33
}
library_dependencies {
  library_index: 217
  library_dep_index: 33
  library_dep_index: 210
}
library_dependencies {
  library_index: 218
  library_dep_index: 33
  library_dep_index: 210
}
library_dependencies {
  library_index: 219
  library_dep_index: 210
  library_dep_index: 33
}
library_dependencies {
  library_index: 220
  library_dep_index: 33
  library_dep_index: 211
  library_dep_index: 212
  library_dep_index: 215
}
library_dependencies {
  library_index: 222
  library_dep_index: 33
  library_dep_index: 89
  library_dep_index: 45
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 109
  library_dep_index: 197
  library_dep_index: 42
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 6
  dependency_index: 7
  dependency_index: 8
  dependency_index: 9
  dependency_index: 20
  dependency_index: 31
  dependency_index: 81
  dependency_index: 82
  dependency_index: 83
  dependency_index: 116
  dependency_index: 167
  dependency_index: 134
  dependency_index: 33
  dependency_index: 1
  dependency_index: 117
  dependency_index: 137
  dependency_index: 166
  dependency_index: 183
  dependency_index: 107
  dependency_index: 187
  dependency_index: 189
  dependency_index: 200
  dependency_index: 201
  dependency_index: 202
  dependency_index: 203
  dependency_index: 118
  dependency_index: 89
  dependency_index: 198
  dependency_index: 204
  dependency_index: 205
  dependency_index: 206
  dependency_index: 45
  dependency_index: 207
  dependency_index: 208
  dependency_index: 121
  dependency_index: 71
  dependency_index: 209
  dependency_index: 123
  dependency_index: 210
  dependency_index: 216
  dependency_index: 217
  dependency_index: 219
  dependency_index: 65
  dependency_index: 3
  dependency_index: 221
  dependency_index: 194
  dependency_index: 150
  dependency_index: 148
  dependency_index: 222
  dependency_index: 218
  dependency_index: 223
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
