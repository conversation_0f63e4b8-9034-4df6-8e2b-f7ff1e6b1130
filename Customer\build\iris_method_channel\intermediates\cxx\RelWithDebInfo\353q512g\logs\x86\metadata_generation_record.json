[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iris_method_channel-2.2.4\\src\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iris_method_channel-2.2.4\\android\\.cxx\\RelWithDebInfo\\353q512g\\x86\\android_gradle_build.json' was up-to-date", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iris_method_channel-2.2.4\\src\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iris_method_channel-2.2.4\\src\\CMakeLists.txt", "tag_": "profile|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]