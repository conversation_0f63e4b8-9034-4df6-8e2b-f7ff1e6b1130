# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.0"
  }
  digests {
    sha256: "\326\371\033{\0170l\312)\237\354t\373|4\344\207Mo^\305\271%\240\264\336!\220\036\021\234?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "\354\270\355\226\275k@O\242^E\003\305\316\336\302,-\354\347\343\275\340\355q:\317\a\\\035-\317"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "\035\341\304\220k\352>\020\026\214\254\027X\372\025\t\237X\021nL\215e\306^\276\"\350\003/\313D"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "|\212\330\322;\270\357\203\302\366z\227!:\276\000\216\035\347\255\225\212\027\224\005nVu\036-7\251"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.3"
  }
  digests {
    sha256: "~E\322\002=\301}q_\377\363\334W\222\367\230d,J\341\034\224\200\006\261u*\271\355\332W\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.7"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.7"
  }
  digests {
    sha256: "p64RZg\261\306\276\252\253O\230\r\372\v\254\337~\024=\v]Q\365\254\r\224\004@\002\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\266\347\205\350S+\025Y!1\005c\251\017\322b\265\332\317id\256\352l\273\344\022\254\035\245\224`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.9.3"
  }
  digests {
    sha256: "\342\3324\366\214\254Uy\206(\001\230\345\301Tk\212$Ri\260P\261\314\220\265i\026\333\366\222\001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.7.1"
  }
  digests {
    sha256: "\217\254\271\a\030\317R^\257\376m\033\326\024]\377\025mj\345\035<\037\202\002\315\005\216\370\201\366*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434"
  }
  digests {
    sha256: "\367\226\251G$\364Y\020\031X\265\311\326q\274\241\266$k\315V\033I\365\177\343\211\023\234\240\307["
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.2.0"
  }
  digests {
    sha256: "\343\336\373^\343\205wWP]z\326bu\334:\v\001Ai0\033\211\326\344\356\300;\016E\274\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.2.0"
  }
  digests {
    sha256: "\2504\302\027\331s\021\'wS=\257\022\201\332\263;\327x\372\335-f\276\2749\265\376\201\250\220\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window.extensions.core"
    artifactId: "core"
    version: "1.0.0"
  }
  digests {
    sha256: "1\000\205K-\300\336\\M\210\264~\034]\263\b\001w\f\202\035\312(\030\224C\267bO@\b\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "me.carda"
    artifactId: "AndroidAwnCore"
    version: "0.10.0"
  }
  digests {
    sha256: "\0009\331%m\037xyB\0049\312Zs\376\021F\205\331\036\341\301\235\037\260]D>\354\032\205J"
  }
  repo_index {
    value: 8
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "33.0.0-android"
  }
  digests {
    sha256: "d\0055\001J0LP\"\032\272\321\235k\"\240\226\3611\031\307\304G\246U\314\036\000\2322 \215"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.2"
  }
  digests {
    sha256: "\212\217\201\317\2335\236?m\372i\032\036wi\205\300a\357/\"<\233,\200u>\033E\216\200d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.41.0"
  }
  digests {
    sha256: "/\237$[\366\216BY\326\020\211O$\006\334\037cc\334c\223\002\275Vn\202r\344\364T\021r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.38.0"
  }
  digests {
    sha256: "fa\3253P\220\245\374a\335\206\235 \225\274l\036!V\343\252G\246\344\253\253\337d\311\232x\211"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "me.leolin"
    artifactId: "ShortcutBadger"
    version: "1.1.22"
  }
  digests {
    sha256: "\315\026\020\334H\305i\222)P!\207\375\303\265\305C8p\325\252\261Y3!!\261\370\301\332\330\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.1.0"
  }
  digests {
    sha256: "\242+\224\247w\211\363\263K\354\322@\202#\023\000R@\021\314\310?\002aB\304\232\326\00117\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.11.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.0"
  }
  digests {
    sha256: "L\023\360\373\000\303z\373f\344\313\307]h\250\n\322\327=g\247\350[\r\244\225\200\250!\273\f9"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "20.7.0"
  }
  digests {
    sha256: "\025\004\224\327\240R\253\252\254\233\270)\242\214\212\322j\303\312Y\305v\301ig\275\276(\252\252N\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.3.0"
  }
  digests {
    sha256: "\224\006jF\004~=Y>\266R8>wg\340c\003\205\354\327Q3\346\214!\022J\251k\215\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.4"
  }
  digests {
    sha256: "\253RK\030r]\220\243\006\r\247\\A\3241\3237\266\331\017^\2259\357\356\337!Zu\336\266?"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.3.0"
  }
  digests {
    sha256: "6\253\374C\227P\270%\355\333?W\a\347\357\002\034K8\354\341\334r\202f\205\256\273\327\000L3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.4"
  }
  digests {
    sha256: "6@\b$\001O\306\035\223y\261=\321\253\023\366QHG\373\0044|\017\337\346\030\272\316x\363\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.6.1"
  }
  digests {
    sha256: "]\r\\\350i\332c\004\2754\342\027\273\367e`\034Q\306\036\305\305\177/!\233\232\210\024\321\370\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-crashlytics"
    version: "19.4.2"
  }
  digests {
    sha256: "\003I\322\217\311\370\274<\370\235YtH\263w\000m#\216 x\314]\375\302JI\344\234\253Ae"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-sessions"
    version: "2.1.0"
  }
  digests {
    sha256: "\304\023\260\241\321X\270\221Sb\313\240\210\231)P\252K\207\365\001\313\215\360t?\002\233($e&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.2.0"
  }
  digests {
    sha256: "\335\033b\b\243h\224\347\327\357\376\"\003\262Du\256\265\fM0E\243\030\020\256:\325\276C\273Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.1"
  }
  digests {
    sha256: "Jt+\237>\366\201\315\3256~\n\a\266\346v\240\203I\306\346\342\024\301\205\024\230\207\223\020\262\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "19.0.0"
  }
  digests {
    sha256: "\005\0035\n\343\021\363\202T:\2108C\325\005\v\243\2355\363?\222\263\311\327\346\026R\210\260\273\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.2.0"
  }
  digests {
    sha256: "\342U-\035\307\\r\200\b\234\t\376\274\257/\356r\207\305\2368\305G\315JC\036\335_M4\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.3.0"
  }
  digests {
    sha256: "*j\235\211\362J\267\337\t3\230:\214Hl!o\263\'G\276\247+1\23162:q\226A\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.3.0"
  }
  digests {
    sha256: "\025\371\301O\300z\214\222@+.\006V\350\351B\222\262E\350\3269],\tm\020\222\r\223\313\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-config-interop"
    version: "16.0.1"
  }
  digests {
    sha256: "m\273\f\361\304nE\375\360;\376g+\247H3`\273\247\000\360\017\016dq\177p\017y\212\177\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "20.0.1"
  }
  digests {
    sha256: "\267k\343\215\245O\227+\303v\3776YIN\353\307\0303\236\3443MBk\264\365.Y\365\352\344"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "25.1.3"
  }
  digests {
    sha256: "\374\376\'\304t~\311\326\021\261h\216\027\366\264\317,\277\263\035\327v\341\260\233\037\252\347\246;\377\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.1"
  }
  digests {
    sha256: "\355\327\206\202D\343\366x\226\357\260\304\306\274\233N\212\262W\v0\217E\312\376G\352\210x\225\346q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.25.5"
  }
  digests {
    sha256: "y\243\377Q\254*\213\033\347\377\372\"\204\203\326It\361\177>\377\377\331f\260T\330qy\344V4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.62.2"
  }
  digests {
    sha256: "%D\222\016\245\364g \334\367^\202\000\243t\320\323v\375\310\362\224({\265yu\272\263\202\2469"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.62.2"
  }
  digests {
    sha256: ".\211iD\317Q>\016\\\3752\274\327,\211`\032\'\306\312V\221o\204\262\017:\023\272\317\033\037"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.62.2"
  }
  digests {
    sha256: "\030C\231\002\304s\242\301Q\036Q}\023\270\256ycx\205\n\216\332Cx|k\247x\372\220\374\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.13.1"
  }
  digests {
    sha256: "\224\205YB\324\231/\021)F\323\336\0343Np\2227\270\022m\2010\277\a\200|\001\212J! "
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.23"
  }
  digests {
    sha256: "\237\376Rk\364:cH\351\330\263;\234\326\365\200\247\365\356\320\317\005Y\023\000~\332&=\351t\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.26.0"
  }
  digests {
    sha256: "\267\322>\223\243E7\3163\'\b&\232\r\024\004x\212[^\031I\350/U5\374\345\033>\251["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.62.2"
  }
  digests {
    sha256: "\231Yt}\366\247S\021\236\034\032=\377\001\252vm$U\365\344\206\n\312\243\0055\236\035S:\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.62.2"
  }
  digests {
    sha256: "\236\220?\375+0\322\373{T\312Mr\221[\032\263[\r\"\274\220\230V[\016\217B(\325\232\376"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-util"
    version: "1.62.2"
  }
  digests {
    sha256: "<q\003\346\363s\205q\343\256\332B\017\342\246\254h\343TSM\213f\364\030\227\266u[H\2675"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.62.2"
  }
  digests {
    sha256: "y\231y\211\250\302\265\277M\321\201\202\242\337./f\207\003\326\213\247\303\027\347\240x\t\323?\221\364"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.62.2"
  }
  digests {
    sha256: "\373L\246y\244!AC@le\254Ag\262\265\342\356,\253\037\301\001Vk\261\304i]\020^6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.1"
  }
  digests {
    sha256: "K\301\327\263\205\307\r\374\006\330c\302\321\020|<\206cS\247\030\020\364\036D\227\250\355\335\231\027\216"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "21.0.1"
  }
  digests {
    sha256: "\365\f\3174\217\366\216\241h\346\327\241\372\356g5\370\322q\3461q\333g\350\270\337\315t\342=w"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "18.0.0"
  }
  digests {
    sha256: "\322z\363\363\034\214\316\241\351\003\f\035\303\204\021\304\v\230g?\027~\3763\344\361\265\021\360\366Y\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.apache.tika"
    artifactId: "tika-core"
    version: "3.1.0"
  }
  digests {
    sha256: "#\2537\350\346\315\227\374w\367\256q\374\031\357\022\256K\221\267\246\251\022\r\360E\2721\374\327\320C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.16"
  }
  digests {
    sha256: "\241%x\335\341\272\000\275\233\201m8\212\v\207\231(\320\v\253<\203\302@\367\001;\364\031lW\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.18.0"
  }
  digests {
    sha256: "\363\312\017\215c\304\016#\245mT\020\034`\325\355\356\023kB\330K\373\205\274yc\t1\t\317\213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.7"
  }
  digests {
    sha256: "\016\217\0302&l[\006g\255=;\020\230\346$\344\232\t\aT\223\240\024\247\350\212\360\037\323\n\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.heifwriter"
    artifactId: "heifwriter"
    version: "1.0.0"
  }
  digests {
    sha256: "\'\017\230}\005\230\314\0221\252\236\243\177Q^KR\317\345\304<\036\240\036\277\033\313@*Z8T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.12.1"
  }
  digests {
    sha256: "\030\240\245\032\321)\340\310V?\003\035F\310\247\346\021i\365j\350NV*\ruhiA&!\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "21.2.0"
  }
  digests {
    sha256: "\274\260mi\312\212D\263X8\216\f \345\017\341ZX\265\223\244\313\231\310\315$\366\340\021[\252\326"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-maps"
    version: "18.2.0"
  }
  digests {
    sha256: "D\365\006UW\212!\305y\341\273-\254\256\304\305E\354M\370\023\223\326\375r6\326d&\200V\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.maps.android"
    artifactId: "android-maps-utils"
    version: "3.6.0"
  }
  digests {
    sha256: "\240\333\357\r\027\225\340Rwf\236\246u\352\212WI\341_\310\356{\3016\v\3461+\351`\211\252"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "review"
    version: "2.0.2"
  }
  digests {
    sha256: "\026\002\002fx#\035\272\017t\202\321$NE\377\203\331\377\"\374\213mk\350\226\022(\254z2\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.4.1"
  }
  digests {
    sha256: "f\315\017\231 \221\221f\b#\323,\217\303)\343\203\250\247\3773\340\326vc#\2076\032\274\272p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.4.1"
  }
  digests {
    sha256: "\227>^{\016\317\216l\214\330%\312\263QE\270LV\002\n\367\303\354R\201^Y^\367\033\252\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.4.1"
  }
  digests {
    sha256: "e\266\322,\226\337\265\316vuK\b\365\260\004\356\r\275\276`y<^\253\237\225\301\365O\225\364\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.4.1"
  }
  digests {
    sha256: "!p\256dH\315I\237\305p\332\372\201\345hz\330\023\231[|+\177\366\200\032\312\252\232\210\273J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.4.1"
  }
  digests {
    sha256: "\034\v\264\036\350\212k\372\362G \273\376-)\2403\253\255!\303\016\t!\357\275\316l\037\242\210B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.4.1"
  }
  digests {
    sha256: "\367\331|Z9\334\243\303\302\033\310\247\001E\3311\330c>\320\332\350\221\230\303p\347\267\343\303\361\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-dash"
    version: "1.4.1"
  }
  digests {
    sha256: "9@\306i5\327\030\320z\272\256\005\261\032\270\227Q\263\000\267\316\344\270\255\241\272\225\020\3476\'Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-hls"
    version: "1.4.1"
  }
  digests {
    sha256: "CTkk.\360\302\201o]:\361\201\302\027\232\\\033\335\271\337\205\267\270K4(\3157\310\207\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-rtsp"
    version: "1.4.1"
  }
  digests {
    sha256: "\206:j\004\327\027.S\020\217}\307\035\23216v\260E\\\372\333S\270\314$a7:\326\316x"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-smoothstreaming"
    version: "1.4.1"
  }
  digests {
    sha256: "\016q@\353\221\a\232bI\372\301p#\aS\312e\224\347j!\375\236\202\345\364\254\361\230\000\347N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.4.1"
  }
  digests {
    sha256: "\037Q\344c>\036a7\360\244\270\005R\257\303\231\342i \213p\003R\371eC\324Q;\346\277("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.pusher"
    artifactId: "pusher-java-client"
    version: "2.4.4"
  }
  digests {
    sha256: "\260\342\205\225\301\343\034%\305\023\035;]D0O][:\310\207y\017:-\363\v\363NZ\274\032"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.java-websocket"
    artifactId: "Java-WebSocket"
    version: "1.5.3"
  }
  digests {
    sha256: "Mo\302\035\311\266W\347m\315t\226\220y\321\2170\347yjV?4\311\246\036e\232H^z?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.journeyapps"
    artifactId: "zxing-android-embedded"
    version: "4.3.0"
  }
  digests {
    sha256: "J\2603S\022|4\345\\\273\200\373\311\243@1\336\313\317\261\354^\217\231\031D\322\324\224b\0323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.zxing"
    artifactId: "core"
    version: "3.5.2"
  }
  digests {
    sha256: "\373\251\001\"\265\325k\254\214\224|`\265\222R\021\260\330\324\fS\2331\t\2767t\360\217t\376\333"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "C\300\336\311\312\005\a\2453\\\267\252\337\024\263\236\350\211\300{N\001Z\276\346ji\227\022f\246~"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 14
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 0
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 11
  library_dep_index: 11
}
library_dependencies {
  library_index: 14
  library_dep_index: 9
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 42
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 44
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 9
  library_dep_index: 17
}
library_dependencies {
  library_index: 18
  library_dep_index: 9
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 9
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 34
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 38
}
library_dependencies {
  library_index: 21
  library_dep_index: 9
}
library_dependencies {
  library_index: 22
  library_dep_index: 9
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 43
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 3
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 29
}
library_dependencies {
  library_index: 28
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 3
}
library_dependencies {
  library_index: 29
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 3
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
  library_dep_index: 9
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 44
  library_dep_index: 37
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 50
  library_dep_index: 45
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 55
}
library_dependencies {
  library_index: 33
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 45
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 51
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 43
}
library_dependencies {
  library_index: 36
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 23
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 38
}
library_dependencies {
  library_index: 37
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 43
}
library_dependencies {
  library_index: 38
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 43
}
library_dependencies {
  library_index: 39
  library_dep_index: 37
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 43
}
library_dependencies {
  library_index: 40
  library_dep_index: 9
  library_dep_index: 19
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 43
  library_dep_index: 37
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 38
}
library_dependencies {
  library_index: 41
  library_dep_index: 9
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 9
}
library_dependencies {
  library_index: 43
  library_dep_index: 9
  library_dep_index: 44
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 39
  library_dep_index: 38
}
library_dependencies {
  library_index: 44
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 14
}
library_dependencies {
  library_index: 45
  library_dep_index: 9
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 45
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 9
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 43
}
library_dependencies {
  library_index: 49
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 47
  library_dep_index: 34
  library_dep_index: 43
}
library_dependencies {
  library_index: 50
  library_dep_index: 9
  library_dep_index: 16
  library_dep_index: 41
  library_dep_index: 17
}
library_dependencies {
  library_index: 51
  library_dep_index: 33
  library_dep_index: 44
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 52
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 38
  library_dep_index: 34
}
library_dependencies {
  library_index: 53
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 55
  library_dep_index: 51
  library_dep_index: 13
  library_dep_index: 44
  library_dep_index: 32
  library_dep_index: 39
  library_dep_index: 49
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 32
}
library_dependencies {
  library_index: 56
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 57
  library_dep_index: 23
  library_dep_index: 36
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 32
  library_dep_index: 9
  library_dep_index: 42
  library_dep_index: 14
  library_dep_index: 58
  library_dep_index: 61
}
library_dependencies {
  library_index: 58
  library_dep_index: 14
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 60
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 58
}
library_dependencies {
  library_index: 60
  library_dep_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
  library_dep_index: 68
  library_dep_index: 9
  library_dep_index: 69
  library_dep_index: 78
  library_dep_index: 8
  library_dep_index: 87
  library_dep_index: 92
  library_dep_index: 94
  library_dep_index: 40
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
  library_dep_index: 17
  library_dep_index: 65
  library_dep_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 69
  library_dep_index: 33
  library_dep_index: 9
  library_dep_index: 70
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 44
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 32
  library_dep_index: 19
  library_dep_index: 34
  library_dep_index: 50
  library_dep_index: 77
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 69
}
library_dependencies {
  library_index: 71
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 72
  library_dep_index: 71
  library_dep_index: 18
  library_dep_index: 11
}
library_dependencies {
  library_index: 73
  library_dep_index: 9
}
library_dependencies {
  library_index: 74
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 54
}
library_dependencies {
  library_index: 75
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 75
  library_dep_index: 75
}
library_dependencies {
  library_index: 77
  library_dep_index: 9
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
  library_dep_index: 67
  library_dep_index: 33
  library_dep_index: 9
  library_dep_index: 69
  library_dep_index: 80
  library_dep_index: 81
  library_dep_index: 82
  library_dep_index: 14
  library_dep_index: 74
  library_dep_index: 84
  library_dep_index: 15
  library_dep_index: 32
  library_dep_index: 19
  library_dep_index: 89
  library_dep_index: 77
  library_dep_index: 90
  library_dep_index: 71
  library_dep_index: 91
}
library_dependencies {
  library_index: 79
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 80
  library_dep_index: 9
}
library_dependencies {
  library_index: 81
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 54
  library_dep_index: 11
}
library_dependencies {
  library_index: 82
  library_dep_index: 69
  library_dep_index: 14
  library_dep_index: 83
}
library_dependencies {
  library_index: 84
  library_dep_index: 14
  library_dep_index: 11
  library_dep_index: 85
}
library_dependencies {
  library_index: 85
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 86
  library_dep_index: 52
  library_dep_index: 87
  library_dep_index: 88
}
library_dependencies {
  library_index: 86
  library_dep_index: 9
}
library_dependencies {
  library_index: 87
  library_dep_index: 9
}
library_dependencies {
  library_index: 88
  library_dep_index: 9
}
library_dependencies {
  library_index: 89
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 54
  library_dep_index: 11
}
library_dependencies {
  library_index: 90
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 84
}
library_dependencies {
  library_index: 91
  library_dep_index: 9
  library_dep_index: 32
  library_dep_index: 89
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 92
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 93
}
library_dependencies {
  library_index: 93
  library_dep_index: 9
  library_dep_index: 92
  library_dep_index: 0
  library_dep_index: 92
}
library_dependencies {
  library_index: 94
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 95
  library_dep_index: 92
  library_dep_index: 93
  library_dep_index: 95
}
library_dependencies {
  library_index: 95
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 94
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
  library_dep_index: 114
  library_dep_index: 117
  library_dep_index: 146
  library_dep_index: 162
  library_dep_index: 166
  library_dep_index: 116
  library_dep_index: 120
  library_dep_index: 122
  library_dep_index: 167
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
  library_dep_index: 11
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 32
  library_dep_index: 87
  library_dep_index: 102
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 0
}
library_dependencies {
  library_index: 98
  library_dep_index: 9
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 18
  library_dep_index: 17
}
library_dependencies {
  library_index: 99
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 105
  library_dep_index: 106
  library_dep_index: 0
  library_dep_index: 99
}
library_dependencies {
  library_index: 101
  library_dep_index: 32
  library_dep_index: 102
  library_dep_index: 104
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 105
  library_dep_index: 30
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 103
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 104
  library_dep_index: 11
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 105
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 106
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 107
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 108
}
library_dependencies {
  library_index: 109
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 107
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 110
  library_dep_index: 111
}
library_dependencies {
  library_index: 112
  library_dep_index: 103
  library_dep_index: 30
}
library_dependencies {
  library_index: 113
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 110
  library_dep_index: 114
}
library_dependencies {
  library_index: 114
  library_dep_index: 29
  library_dep_index: 115
  library_dep_index: 110
  library_dep_index: 9
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 115
  library_dep_index: 110
  library_dep_index: 9
  library_dep_index: 67
}
library_dependencies {
  library_index: 116
  library_dep_index: 114
  library_dep_index: 3
  library_dep_index: 115
  library_dep_index: 110
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
  library_dep_index: 30
  library_dep_index: 110
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 144
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 122
  library_dep_index: 119
  library_dep_index: 145
  library_dep_index: 3
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 9
}
library_dependencies {
  library_index: 118
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 119
  library_dep_index: 110
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 3
  library_dep_index: 122
  library_dep_index: 123
  library_dep_index: 124
  library_dep_index: 111
  library_dep_index: 9
  library_dep_index: 128
}
library_dependencies {
  library_index: 119
  library_dep_index: 30
  library_dep_index: 110
}
library_dependencies {
  library_index: 120
  library_dep_index: 9
}
library_dependencies {
  library_index: 121
  library_dep_index: 3
  library_dep_index: 9
  library_dep_index: 120
}
library_dependencies {
  library_index: 122
  library_dep_index: 30
  library_dep_index: 110
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 119
  library_dep_index: 0
}
library_dependencies {
  library_index: 123
  library_dep_index: 9
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 3
}
library_dependencies {
  library_index: 124
  library_dep_index: 9
}
library_dependencies {
  library_index: 125
  library_dep_index: 124
  library_dep_index: 126
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 9
}
library_dependencies {
  library_index: 126
  library_dep_index: 124
  library_dep_index: 9
  library_dep_index: 111
  library_dep_index: 120
  library_dep_index: 127
}
library_dependencies {
  library_index: 127
  library_dep_index: 9
  library_dep_index: 120
}
library_dependencies {
  library_index: 128
  library_dep_index: 129
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
  library_dep_index: 140
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 131
  library_dep_index: 9
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 138
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 128
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
}
library_dependencies {
  library_index: 133
  library_dep_index: 9
  library_dep_index: 134
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 130
  library_dep_index: 136
  library_dep_index: 128
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 134
  library_dep_index: 0
  library_dep_index: 135
}
library_dependencies {
  library_index: 135
  library_dep_index: 0
}
library_dependencies {
  library_index: 136
  library_dep_index: 137
}
library_dependencies {
  library_index: 137
  library_dep_index: 132
  library_dep_index: 138
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 128
  library_dep_index: 140
  library_dep_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 138
  library_dep_index: 139
}
library_dependencies {
  library_index: 139
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 140
  library_dep_index: 141
}
library_dependencies {
  library_index: 141
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 142
  library_dep_index: 138
  library_dep_index: 0
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 128
  library_dep_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 142
  library_dep_index: 143
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 128
  library_dep_index: 140
  library_dep_index: 143
}
library_dependencies {
  library_index: 143
  library_dep_index: 130
  library_dep_index: 132
  library_dep_index: 136
  library_dep_index: 128
  library_dep_index: 140
  library_dep_index: 142
}
library_dependencies {
  library_index: 144
  library_dep_index: 121
  library_dep_index: 120
}
library_dependencies {
  library_index: 145
  library_dep_index: 31
  library_dep_index: 110
}
library_dependencies {
  library_index: 146
  library_dep_index: 147
  library_dep_index: 30
  library_dep_index: 110
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 149
  library_dep_index: 9
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 150
  library_dep_index: 158
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 0
  library_dep_index: 25
}
library_dependencies {
  library_index: 147
  library_dep_index: 148
}
library_dependencies {
  library_index: 149
  library_dep_index: 103
}
library_dependencies {
  library_index: 150
  library_dep_index: 151
  library_dep_index: 152
  library_dep_index: 63
}
library_dependencies {
  library_index: 151
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 63
}
library_dependencies {
  library_index: 152
  library_dep_index: 151
  library_dep_index: 153
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 156
  library_dep_index: 157
}
library_dependencies {
  library_index: 153
  library_dep_index: 67
}
library_dependencies {
  library_index: 157
  library_dep_index: 151
}
library_dependencies {
  library_index: 158
  library_dep_index: 151
  library_dep_index: 159
  library_dep_index: 152
  library_dep_index: 138
  library_dep_index: 63
  library_dep_index: 156
}
library_dependencies {
  library_index: 159
  library_dep_index: 151
  library_dep_index: 152
  library_dep_index: 155
  library_dep_index: 63
}
library_dependencies {
  library_index: 160
  library_dep_index: 151
  library_dep_index: 148
  library_dep_index: 65
  library_dep_index: 63
}
library_dependencies {
  library_index: 161
  library_dep_index: 151
  library_dep_index: 63
  library_dep_index: 67
}
library_dependencies {
  library_index: 162
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 123
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 127
  library_dep_index: 163
  library_dep_index: 122
  library_dep_index: 119
  library_dep_index: 145
  library_dep_index: 9
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 67
  library_dep_index: 0
}
library_dependencies {
  library_index: 163
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 164
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 165
  library_dep_index: 85
  library_dep_index: 31
}
library_dependencies {
  library_index: 166
  library_dep_index: 110
  library_dep_index: 167
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 116
  library_dep_index: 115
  library_dep_index: 9
  library_dep_index: 103
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 25
}
library_dependencies {
  library_index: 167
  library_dep_index: 30
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 9
  library_dep_index: 103
  library_dep_index: 0
}
library_dependencies {
  library_index: 168
  library_dep_index: 169
  library_dep_index: 170
}
library_dependencies {
  library_index: 171
  library_dep_index: 9
}
library_dependencies {
  library_index: 172
  library_dep_index: 9
}
library_dependencies {
  library_index: 173
  library_dep_index: 9
  library_dep_index: 15
  library_dep_index: 14
}
library_dependencies {
  library_index: 174
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 18
}
library_dependencies {
  library_index: 175
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 25
}
library_dependencies {
  library_index: 176
  library_dep_index: 32
  library_dep_index: 103
  library_dep_index: 31
  library_dep_index: 30
}
library_dependencies {
  library_index: 178
  library_dep_index: 31
  library_dep_index: 30
  library_dep_index: 108
}
library_dependencies {
  library_index: 179
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 171
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 183
  library_dep_index: 184
  library_dep_index: 189
  library_dep_index: 182
}
library_dependencies {
  library_index: 180
  library_dep_index: 9
  library_dep_index: 63
  library_dep_index: 15
  library_dep_index: 181
  library_dep_index: 182
  library_dep_index: 183
  library_dep_index: 184
  library_dep_index: 179
  library_dep_index: 185
  library_dep_index: 186
  library_dep_index: 187
  library_dep_index: 188
  library_dep_index: 189
}
library_dependencies {
  library_index: 181
  library_dep_index: 9
  library_dep_index: 180
}
library_dependencies {
  library_index: 182
  library_dep_index: 180
  library_dep_index: 9
}
library_dependencies {
  library_index: 183
  library_dep_index: 180
  library_dep_index: 182
  library_dep_index: 9
  library_dep_index: 171
}
library_dependencies {
  library_index: 184
  library_dep_index: 180
  library_dep_index: 9
}
library_dependencies {
  library_index: 185
  library_dep_index: 179
  library_dep_index: 9
}
library_dependencies {
  library_index: 186
  library_dep_index: 9
  library_dep_index: 179
}
library_dependencies {
  library_index: 187
  library_dep_index: 9
  library_dep_index: 179
}
library_dependencies {
  library_index: 188
  library_dep_index: 179
  library_dep_index: 9
}
library_dependencies {
  library_index: 189
  library_dep_index: 9
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 184
}
library_dependencies {
  library_index: 190
  library_dep_index: 153
  library_dep_index: 191
}
library_dependencies {
  library_index: 191
  library_dep_index: 169
}
library_dependencies {
  library_index: 194
  library_dep_index: 9
  library_dep_index: 69
  library_dep_index: 14
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 89
  library_dep_index: 195
  library_dep_index: 11
}
library_dependencies {
  library_index: 195
  library_dep_index: 9
  library_dep_index: 54
  library_dep_index: 14
  library_dep_index: 59
  library_dep_index: 90
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 6
  dependency_index: 7
  dependency_index: 8
  dependency_index: 57
  dependency_index: 62
  dependency_index: 63
  dependency_index: 96
  dependency_index: 146
  dependency_index: 114
  dependency_index: 9
  dependency_index: 14
  dependency_index: 19
  dependency_index: 168
  dependency_index: 44
  dependency_index: 97
  dependency_index: 117
  dependency_index: 162
  dependency_index: 87
  dependency_index: 166
  dependency_index: 40
  dependency_index: 171
  dependency_index: 172
  dependency_index: 170
  dependency_index: 173
  dependency_index: 98
  dependency_index: 69
  dependency_index: 174
  dependency_index: 175
  dependency_index: 176
  dependency_index: 177
  dependency_index: 33
  dependency_index: 178
  dependency_index: 103
  dependency_index: 179
  dependency_index: 185
  dependency_index: 186
  dependency_index: 188
  dependency_index: 2
  dependency_index: 190
  dependency_index: 153
  dependency_index: 192
  dependency_index: 193
  dependency_index: 130
  dependency_index: 128
  dependency_index: 194
  dependency_index: 187
  dependency_index: 196
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
